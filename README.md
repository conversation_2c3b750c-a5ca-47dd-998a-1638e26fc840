# prompts-library
This repository contains a collection of prompts to answer various things about companies based mostly on their scraped websites.

## Prompts
Prompts are YAML files under `/prompts` directory. Each file contains a sequence of tasks; downstream tasks can use the output of upstream tasks.

## Running
`driver.py` runs the given prompts file for <PERSON>. `driver_local.py` is for local testing

## Output
The output is written to a Postgres database.
