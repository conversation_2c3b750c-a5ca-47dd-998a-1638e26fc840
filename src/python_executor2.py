import subprocess
import sys
import json
import tempfile
import os
from typing import Dict, Any


def execute_python_code(code: str, timeout: int = 30, env: dict | None = None) -> Dict[str, Any]:
    """
    Execute Python code safely in a subprocess and return the results.

    Returns: Dict with keys: output, error, success, variables
    """

    snippet = code[:100]
    indented_snippet = "\n\t".join(snippet.splitlines())
    print("Executing Python code (first 100 characters):\n\t" + indented_snippet)

    # Create a temporary file with the code
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
        indented_code = '\n'.join('        ' + line for line in code.split('\n'))

        wrapped_code = f"""
import sys
import json
import math
from datetime import datetime, date
import re
import io
import os
from contextlib import redirect_stdout

# --- Helpers made available to user code ---
def read_blob_text(encoding="utf-8", errors="ignore"):
    p = os.environ.get("PY_TOOL_BLOB_PATH", "")
    if not p:
        return ""
    try:
        with open(p, "r", encoding=encoding, errors=errors) as fh:
            return fh.read()
    except Exception as _:
        return ""
        
# --- Tee stdout to both a buffer and the real stdout (for live prints) ---
class Tee(io.TextIOBase):
    def __init__(self, *streams):
        self.streams = streams
    def write(self, s):
        # prefix every line of user output
        for line in s.splitlines(True):  # keep newline chars
            prefixed = "FROM_PROMPT: " + line if line.strip() else line
            for st in self.streams:
                st.write(prefixed)
                st.flush()
        return len(s)
    def flush(self):
        for st in self.streams:
            st.flush()

output_buffer = io.StringIO()
tee = Tee(output_buffer, sys.stdout)

try:
    with redirect_stdout(tee):
        # User code starts here
{indented_code}
        # User code ends here

    # Capture local variables (excluding built-ins and imports)
    local_vars = {{k: v for k, v in locals().items()
                  if not k.startswith('_') and k not in
                  ['sys','json','math','datetime','date','re','io','redirect_stdout','output_buffer','tee']}}

    # Convert variables to JSON-serializable format
    serializable_vars = {{}}
    for k, v in local_vars.items():
        try:
            json.dumps(v)
            serializable_vars[k] = v
        except (TypeError, ValueError):
            serializable_vars[k] = str(v)

    result = {{
        'output': output_buffer.getvalue(),
        'error': '',
        'success': True,
        'variables': serializable_vars
    }}

except Exception as e:
    result = {{
        'output': output_buffer.getvalue(),
        'error': str(e),
        'success': False,
        'variables': {{}}
    }}

# IMPORTANT: print the machine-readable result to STDERR
print("PYTHON_EXECUTOR_RESULT:" + json.dumps(result), file=sys.stderr)
"""
        f.write(wrapped_code)
        temp_file_path = f.name

    try:
        merged_env = os.environ.copy()
        if env:
            merged_env.update(env)
        # NOTE: inherit child's STDOUT so prints appear LIVE in your console
        process = subprocess.run(
            [sys.executable, temp_file_path],
            stdout=None,                # <- live streaming
            stderr=subprocess.PIPE,     # <- we parse JSON from here
            text=True,
            timeout=timeout,
            env=merged_env
        )

        # Parse the JSON result line from STDERR
        result_line = None
        if process.stderr:
            for line in process.stderr.splitlines():
                if line.startswith("PYTHON_EXECUTOR_RESULT:"):
                    result_line = line[len("PYTHON_EXECUTOR_RESULT:"):]
                    break

        if result_line:
            result = json.loads(result_line)
        else:
            result = {
                'output': '',  # no buffered stdout here; prints already went live
                'error': process.stderr if process.stderr else f"No result line found. Return code: {process.returncode}",
                'success': process.returncode == 0,
                'variables': {}
            }

    except subprocess.TimeoutExpired:
        result = {
            'output': '',
            'error': f'Code execution timed out after {timeout} seconds',
            'success': False,
            'variables': {}
        }
    except Exception as e:
        result = {
            'output': '',
            'error': f'Execution error: {str(e)}',
            'success': False,
            'variables': {}
        }
    finally:
        try:
            os.unlink(temp_file_path)
        except:
            pass

    return result


# Tool definition unchanged
PYTHON_EXECUTOR_TOOL = {
    "type": "function",
    "function": {
        "name": "execute_python_code",
        "description": "Execute Python code for calculations, data processing, and simple operations. Use this when you need to perform mathematical calculations, process lists, count items, or do any computational work that would be easier with code than manual reasoning.",
        "parameters": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string",
                    "description": "Python code to execute. Can include calculations, list operations, counting, etc. Available modules: math, datetime, re, json. Use print() to output results."
                }
            },
            "required": ["code"]
        }
    }
}
