metadata:
  owner: "Damjan"
  markdown_db_table: "wget2_runs"

prompts:
  - name: find_team_section
    prompt: |    
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      You are given a company's website in markdown.
      Identify sections that list employees. Look for headings like "Team", "Our People", "Management", "Leadership", "Staff", "Board", or similar. 
      Return only the section text if found. If no team page exists, return exactly "no_team_page". 
      
      <markdown>{markdown}</markdown>
  
      Respond with a JSON object like:
      {{
        "team_section": ...,   // string, the team section text. "no_team_page" if no team page exists.
      }}
      
      But don't preface it with ```json.

  - name: is_team_section_complete
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
  
      You are given a section of a company's website that lists employees:
      
      input: {find_team_section[team_section]}
  
      Decide if this section represents:
      - all employees of the company ("full")
      - only a subset, such as executives or a department ("partial")
      
      You can assume full if there is more than 10 people mentioned, and preferably have multiple categories / roles, not just management.
      
      Respond with a JSON object like:
      {{
        "team_section_complete": "full" or "partial"
      }}
  
      But don't preface it with ```json.

  - name: count_employees
    prompt: |
      You are given markdown of a company website section.
      
      input: {find_team_section[team_section]}
  
      If it is too short, do not make up examples. Instead skip the below steps and return this JSON:
      {{"status": "Too short to be a team section"}}
    
  
      STEP 1: Extract a list of all individual team member names mentioned. Ignore headings, job titles, and company names. Only return real person names.
      The names should be extracted from your reasoning, not by executing Python code! Do not make up any names! Do not write any code for STEP 1!
  
      STEP 2: You MUST use the Python executor tool (execute_python_code) to count the number of names from step 1.
      Do not calculate this manually - you must use the tool! Here is an example, just plugin the list of names from step 1 and follow the example.
  
      Example code:
      ```python
      names_list = ["Name1", "Name2", "Name3"]  # Replace with actual names from step 1
      employee_count = len(names_list)
      print(f"Employee count: " + str(employee_count))
      ```
  
      STEP 3: You MUST use Python (execute_python_code) to count the characters in the input text.
      Do not calculate this manually - you must use the tool!
  
      Example code:
      ```python
      markdown_text = '''the actual markdown content here'''
      char_count = len(input)
      print("Character count: " + str(char_count))
      if char_count < 50:
          print("Status: Too short to be a team section")
      else:
          print("Status: OK")
      ```
  
      IMPORTANT: You must execute Python code for steps 2 and 3. Do not provide these numbers without using the execute_python_code tool!
      Never use regex / re / regular expressions to extract names or count characters.
  
  
      Output:
      After executing the Python code, respond with a JSON object like:
      {{
        "employee_names": [...]   // list of strings from step 1
        "employee_count": ...   // int from Python execution in step 2
        "status": ...   // string, "Too short to be a team section" or "OK" based on step 3
        "char_count": ...   // int from Python execution in step 3
      }}
  
      But don't preface it with ```json.
  
