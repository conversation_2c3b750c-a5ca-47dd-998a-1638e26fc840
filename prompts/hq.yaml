metadata:
  owner: "<PERSON>"
  markdown_db_table: "wget2_runs"

prompts:
  - name: extract_chunk
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      You extract a company's primary location sentence from the below markdown input. 
      Only return a sentence that asserts HQ/base/main office (examples: "headquartered in", "based in", "our headquarters are in"; multilingual equivalents like "Hauptsitz in", "siège social à", "sede central en", "总部在").
  
      Reject:
      - generic contact/address blocks, PO boxes, room/floor/suite mentions
      - branch/warehouse/office listings, "offices in X, Y, Z", "global presence" lists
      - job or event locations, travel directions
      - vague mentions like "in Europe", "across Germany", or "founded in Berlin" (founding place is not the same as HQ unless the sentence states HQ/base)
      - Prefer global/corporate HQ over regional HQ if both appear.
  
      Return one qualifying sentence at most. If none exists, return Unknown.
      Your output must be strict JSON with keys: location_mention, quote. No extra keys, no explanations.
  
      Rules:
      (1) quote = the single sentence (verbatim) that asserts HQ/base/main office.
      (2) location_mention = the minimal location span inside that sentence (verbatim), which can be:
         - "City"
         - "City, State"
         - "City, Country"
      (3) If multiple HQ sentences exist, choose the clearest global/corporate HQ.
      (4) If no qualifying sentence exists, return: "location_mention": "Unknown", "quote": "Unknown"
      
      Respond with a JSON object like:
  
      {{
        "location_mention": ...  // the minimal location span inside the sentence
        "quote": ... // the single sentence that asserts HQ/base/main office
      }}
      
      But don't preface it with ```json.
  
      input:
      <markdown>
      {markdown}
      </markdown>

  - name: validate_city_country
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      Normalize a location from a single sentence (the quote). Output exactly "City, Country".
  
      Constraints:
        - Use ONLY the information in the quote; may use general world knowledge ONLY to infer a city's country if unambiguous
          (e.g., "Copenhagen" → Denmark; "Cambridge" ambiguous → Unknown unless the quote disambiguates).
        - If the quote names State/Province (e.g., US states or Canadian provinces), map to the correct Country
          (e.g., "Austin, Texas" → "Austin, United States"; "Toronto, Ontario" → "Toronto, Canada").
        - Countries must be in unified English.
        - City should be in standard local/English form with correct capitalization and diacritics.
        - Ignore street addresses, floors, suites, postal codes; if the quote is not about HQ/base/location or is otherwise non-location, return Unknown.
        - If multiple cities appear and the quote doesn’t single one out as HQ/base/main office, return Unknown.
  
      Output must be strict JSON with key "location".
  
      Respond with a JSON object like:
      {{
        "location": ...  // "City, Country" or "Unknown"
      }}
  
      But don't preface it with ```json.
  
      Input:
      - input: {extract_chunk[quote]}