metadata:
  owner: "<PERSON>"
  markdown_db_table: "imprint_runs"

prompts:
  - name: find_information
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      You are given a company's imprints statement in a markdown format.
      Markdown: {markdown}
  
      You are an AI assistant tasked with extracting and normalizing postal address and phone number from raw markdown content.
      Return a JSON object with exactly these keys: address & phone_number.
      - address: full address (street, house number, postal code, city, country), or 'Unknown'
      - phone_number: normalize every German phone number into the format:
          1) Start with '+49'
          2) Then exactly one space
          3) Then the area code with its leading '0' removed
          4) Then exactly one space
          5) Then the subscriber number as a continuous string of digits (no other spaces or punctuation)
      If no phone number is found, return 'Unknown'.
      
      {{
        "address": ...,  
        "phone_number": "..."
      }}
  
      But don't preface it with ```json.