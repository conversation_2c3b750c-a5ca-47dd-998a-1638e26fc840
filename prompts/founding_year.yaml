metadata:
  owner: "<PERSON>"
  markdown_db_table: "wget2_runs"

prompts:
  - name: find_quote
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      Task:
      You are given a company's website in markdown (TEXT):
      - From TEXT, extract ONE passage that states the founding/establishment of a company or organization.
      - The passage MUST contain:
        -- a strict founding cue (e.g., founded, established, incorporated, gegründet, Gründung, fondé, opgericht, stiftet, grundades, grundlagt, fondato/fondata, fundado/fundada, constituita/constituida, założona/založen, alapítva, perustettu, kuruldu, основан/создан, ιδρύθηκε, تأسست, נוסדה, 成立/設立/創立),
        -- a 4-digit year (YYYY),
        -- the cue and the year within <=12 words of each other (any order),
        -- a reference to an organization (not a person/CV/role/product/event/news).
      - Put the extracted passage as the value of "quote", 'wrapped in single quotes'; the value MUST begin with `'` and end with `'`.
      - Return the full sentence containing cue+year; include 1–2 context sentences if available and relevant.
      - REJECT and return {{"quote": ""}} if the passage contains any of: personal bios, links/paths/images/markdown/news tokens, year ranges (e.g., 2021–2024 / 2021 bis 2024 / 2021-2024), vague “since … our name stands for …”.
  
      Respond with a JSON object like:
  
      {{
        "quote": ... // string, the quote text or ""
      }}
      
      But don't preface it with ```json.
  
      <markdown>
      {markdown}
      </markdown>

  - name: extract_number
    prompt: |
      For the below task use only your reasoning, do NOT use external tools like Python execution/interpreter! Do NOT write any code!
      
      You are a JSON generator. Return JSON ONLY with this exact shape:
      {{"is_founding": true/false, "year": "YYYY", "source": ""}}
  
      Task:
      - Decide if the input truly states the founding/establishment of a company or organization.
      - Valid ONLY if ALL are true:
      -- QUOTE uses a strict founding cue (as in: founded/established/incorporated/ gegründet / Gründung / fondé / opgericht / stiftet / grundades / grundlagt / fondato/fondata / fundado/fundada / constituita/constituida / założona/založen / alapítva / perustettu / kuruldu / основан/создан / ιδρύθηκε / تأسست / נוסדה / 成立/設立/創立),
      -- QUOTE contains a 4-digit year (YYYY) within <=12 words of that cue (any order),
      -- QUOTE is about an organization (not a person/CV/role/certification/product/event/news),
      -- Year is not a range, and QUOTE has no links/paths/images/markdown/news tokens.
      - If valid:
      -- Set is_founding = true,
      -- Put the 4-digit year into "year",
      -- Put into "source" a <=100-character snippet that STILL includes the same 4-digit year, **wrapped in single quotes**; the value MUST begin with `'` and end with `'`.
      - If invalid: {{"is_founding": false, "year": "", "source": ""}}
  
      Respond with a JSON object like:
      {{
        "is_founding": true/false,
        "year": "YYYY",
        "source": "..."
      }}
  
      But don't preface it with ```json.
  
      Input:
      - input: {find_quote[quote]}