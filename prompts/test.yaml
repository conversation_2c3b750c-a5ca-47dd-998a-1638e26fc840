metadata:
  owner: "<PERSON><PERSON>"
  markdown_db_table: "xxx"

prompts:
  - name: test_step1
    prompt: |
      You are given markdown of a company website section.
  
      input: {markdown}
  
      From it extract the company name.
      Use Python (execute_python_code) to count the length of the characters in the company name.
      Make sure to assign the result to a variable so it can be captured.
      
      Example code:
      ```python
      text = """extracted company name"""
      length = len(text)
      print("Character count: %text%")
      ```
      
      Output: a JSON with the value of the variable "length" and the "company_name" that you extracted.
      {{
        "length": ...
        "company_name": ...
      }}
        But don't preface it with ```json.

  - name: test_step2
    prompt: |
      You are given a string, use Python (execute_python_code) to reverse it and make it uppercase.
  
      input: {test_step1[company_name]}
  
      Make sure to assign the result to a variable so it can be captured.
  
      Example code:
      ```python
        s = "the input string"
        reversed_s = s[::-1]
        uppercase_s = reversed_s.upper()
        print("Reversed and uppercase string: " + uppercase_s)
      ```
  
      Output: a JSON with the value of the variable "uppercase_s".
      {{
        "uppercase_s": ...
      }}
        But don't preface it with ```json.