#!/usr/bin/env python3
import argparse, math, json, re
from pathlib import Path
import pandas as pd

def slugify(s: str, max_len: int = 48) -> str:
    """Make a filesystem-friendly label: lowercase, keep [a-z0-9-_], collapse dashes."""
    if s is None:
        s = ""
    s = str(s).strip().lower()
    # replace spaces with dashes
    s = re.sub(r"\s+", "-", s)
    # keep only allowed chars
    s = re.sub(r"[^a-z0-9._-]", "-", s)
    # collapse multiples
    s = re.sub(r"-{2,}", "-", s).strip("-")
    if not s:
        s = "na"
    return s[:max_len]

def split(input_csv: str, chunk_dir: str, workers: int):
    inp = Path(input_csv)
    out = Path(chunk_dir)
    out.mkdir(parents=True, exist_ok=True)

    df = pd.read_csv(inp, dtype=str).fillna("")
    if df.shape[1] == 0:
        raise SystemExit("CSV has no columns")
    total = len(df)
    if total == 0:
        raise SystemExit("No data rows found")
    if workers < 1:
        raise SystemExit("workers must be >=1")
    if workers > total:
        workers = total

    key_col = 'cib_id' #df.columns[0]  # use the FIRST column to derive first/last labels
    per_chunk = math.ceil(total / workers)

    manifest = {"input": str(inp), "key_col": key_col, "chunks": []}
    chunk_list = []

    start = 0

    print(f"[fanout] Splitting {total} rows into {workers} chunks of ~{per_chunk} rows each")
    print(df.head())

    for i in range(workers):
        end = min(start + per_chunk, total)
        if start >= end:
            break
        part = df.iloc[start:end]
        first_val = str(part[key_col].iloc[0]) if len(part) else ""
        last_val  = str(part[key_col].iloc[-1]) if len(part) else ""
        first_s = slugify(first_val)
        last_s  = slugify(last_val)

        chunk_name = f"chunk-{i+1:02d}-{first_s}-{last_s}.csv"
        chunk_path = out / chunk_name
        part.to_csv(chunk_path, index=False)

        (out / f"chunk-{i+1:02d}.range").write_text(f"{first_val}-{last_val}", encoding="utf-8")

        manifest["chunks"].append({
            "index": i+1,
            "csv": str(chunk_path),
            "rows": len(part),
            "first": first_val,
            "last": last_val,
            "label": f"{first_s}-{last_s}",
        })
        chunk_list.append(chunk_name)
        start = end

    (out / "chunks.txt").write_text("\n".join(chunk_list) + "\n", encoding="utf-8")
    (out / "manifest.json").write_text(json.dumps(manifest, indent=2), encoding="utf-8")
    print(f"[fanout] Split {total} rows into {len(chunk_list)} chunks at {out} using key_col='{key_col}'")

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument("--input-csv", required=True)
    ap.add_argument("--chunk-dir", required=True)
    ap.add_argument("--workers", type=int, required=True)
    args = ap.parse_args()
    split(args.input_csv, args.chunk_dir, args.workers)

if __name__ == "__main__":
    main()
